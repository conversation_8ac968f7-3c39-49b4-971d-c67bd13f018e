File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,15.761885%,8269,96,7718,551,0,96
sdio_sdcard.o,13.667035%,7170,68,7134,0,36,32
ff.o,9.812817%,5148,6,5142,0,6,0
oled.o,8.249781%,4328,22,1594,2712,22,0
ebtn.o,3.945713%,2070,60,2070,0,0,60
btod.o,3.690290%,1936,0,1936,0,0,0
mcu_cmic_gd32f470vet6.o,3.431055%,1800,616,1780,0,20,596
sd_app.o,3.030765%,1590,1452,1574,0,16,1436
gd32f4xx_dma.o,2.538980%,1332,0,1332,0,0,0
gd25qxx.o,2.279745%,1196,0,1196,0,0,0
_printf_fp_dec.o,2.009073%,1054,0,1054,0,0,0
gd30ad3344.o,1.890893%,992,8,984,0,8,0
gd32f4xx_rcu.o,1.646906%,864,0,864,0,0,0
perf_counter.o,1.616408%,848,64,780,4,64,0
_printf_fp_hex.o,1.528726%,802,0,764,38,0,0
system_gd32f4xx.o,1.330487%,698,4,694,0,4,0
gd32f4xx_adc.o,1.246617%,654,0,654,0,0,0
gd32f4xx_usart.o,1.227555%,644,0,644,0,0,0
gd32f4xx_sdio.o,1.197057%,628,0,628,0,0,0
gd32f4xx_timer.o,1.120811%,588,0,588,0,0,0
btn_app.o,1.075064%,564,196,354,14,196,0
gd32f4xx_i2c.o,0.945446%,496,0,496,0,0,0
startup_gd32f450_470.o,0.937822%,492,2048,64,428,0,2048
gd32f4xx_rtc.o,0.922573%,484,0,484,0,0,0
__printf_flags_ss_wp.o,0.779612%,409,0,392,17,0,0
bigflt0.o,0.716709%,376,0,228,148,0,0
lc_ctype_c.o,0.602341%,316,0,44,272,0,0
diskio.o,0.602341%,316,0,316,0,0,0
gd32f4xx_gpio.o,0.499409%,262,0,262,0,0,0
fz_wm.l,0.487972%,256,0,256,0,0,0
led_app.o,0.478442%,251,7,244,0,7,0
oled_app.o,0.465099%,244,0,244,0,0,0
lludivv7m.o,0.453662%,238,0,238,0,0,0
gd32f4xx_misc.o,0.411727%,216,0,216,0,0,0
gd32f4xx_dac.o,0.377416%,198,0,198,0,0,0
_printf_wctomb.o,0.373604%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.358355%,188,0,148,40,0,0
_printf_intcommon.o,0.339293%,178,0,178,0,0,0
scheduler.o,0.335481%,176,76,100,0,76,0
systick.o,0.320232%,168,4,164,0,4,0
gd32f4xx_it.o,0.320232%,168,0,168,0,0,0
usart_app.o,0.303077%,159,515,156,0,3,512
perfc_port_default.o,0.293546%,154,0,154,0,0,0
fnaninf.o,0.266860%,140,0,140,0,0,0
rt_memcpy_v6.o,0.263048%,138,0,138,0,0,0
lludiv10.o,0.263048%,138,0,138,0,0,0
strcmpv7m.o,0.243986%,128,0,128,0,0,0
_printf_fp_infnan.o,0.243986%,128,0,128,0,0,0
_printf_longlong_dec.o,0.236362%,124,0,124,0,0,0
_printf_dec.o,0.228737%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.213488%,112,0,112,0,0,0
adc_app.o,0.213488%,112,0,112,0,0,0
gd32f4xx_spi.o,0.198239%,104,0,104,0,0,0
rt_memcpy_w.o,0.190614%,100,0,100,0,0,0
__dczerorl2.o,0.171553%,90,0,90,0,0,0
main.o,0.171553%,90,0,90,0,0,0
memcmp.o,0.167740%,88,0,88,0,0,0
f2d.o,0.163928%,86,0,86,0,0,0
_printf_str.o,0.156304%,82,0,82,0,0,0
rt_memclr_w.o,0.148679%,78,0,78,0,0,0
_printf_pad.o,0.148679%,78,0,78,0,0,0
sys_stackheap_outer.o,0.141054%,74,0,74,0,0,0
llsdiv.o,0.137242%,72,0,72,0,0,0
lc_numeric_c.o,0.137242%,72,0,44,28,0,0
rt_memclr.o,0.129618%,68,0,68,0,0,0
_wcrtomb.o,0.121993%,64,0,64,0,0,0
strlen.o,0.118181%,62,0,62,0,0,0
rtc_app.o,0.114368%,60,0,60,0,0,0
vsnprintf.o,0.099119%,52,0,52,0,0,0
__scatter.o,0.099119%,52,0,52,0,0,0
m_wm.l,0.091495%,48,0,48,0,0,0
fpclassify.o,0.091495%,48,0,48,0,0,0
_printf_char_common.o,0.091495%,48,0,48,0,0,0
_printf_wchar.o,0.083870%,44,0,44,0,0,0
_printf_char.o,0.083870%,44,0,44,0,0,0
__2sprintf.o,0.083870%,44,0,44,0,0,0
_printf_charcount.o,0.076246%,40,0,40,0,0,0
libinit2.o,0.072433%,38,0,38,0,0,0
init_aeabi.o,0.068621%,36,0,36,0,0,0
_printf_truncate.o,0.068621%,36,0,36,0,0,0
systick_wrapper_ual.o,0.060997%,32,0,32,0,0,0
__scatter_zi.o,0.053372%,28,0,28,0,0,0
gd32f4xx_pmu.o,0.038123%,20,0,20,0,0,0
exit.o,0.034311%,18,0,18,0,0,0
rt_ctype_table.o,0.030498%,16,0,16,0,0,0
_snputc.o,0.030498%,16,0,16,0,0,0
__printf_wp.o,0.026686%,14,0,14,0,0,0
dretinf.o,0.022874%,12,0,12,0,0,0
sys_exit.o,0.022874%,12,0,12,0,0,0
__rtentry2.o,0.022874%,12,0,12,0,0,0
fpinit.o,0.019061%,10,0,10,0,0,0
rtexit2.o,0.019061%,10,0,10,0,0,0
_sputc.o,0.019061%,10,0,10,0,0,0
_printf_ll.o,0.019061%,10,0,10,0,0,0
_printf_l.o,0.019061%,10,0,10,0,0,0
rt_locale_intlibspace.o,0.015249%,8,0,8,0,0,0
libspace.o,0.015249%,8,96,8,0,0,96
__main.o,0.015249%,8,0,8,0,0,0
heapauxi.o,0.011437%,6,0,6,0,0,0
_printf_x.o,0.011437%,6,0,6,0,0,0
_printf_u.o,0.011437%,6,0,6,0,0,0
_printf_s.o,0.011437%,6,0,6,0,0,0
_printf_p.o,0.011437%,6,0,6,0,0,0
_printf_o.o,0.011437%,6,0,6,0,0,0
_printf_n.o,0.011437%,6,0,6,0,0,0
_printf_ls.o,0.011437%,6,0,6,0,0,0
_printf_llx.o,0.011437%,6,0,6,0,0,0
_printf_llu.o,0.011437%,6,0,6,0,0,0
_printf_llo.o,0.011437%,6,0,6,0,0,0
_printf_lli.o,0.011437%,6,0,6,0,0,0
_printf_lld.o,0.011437%,6,0,6,0,0,0
_printf_lc.o,0.011437%,6,0,6,0,0,0
_printf_i.o,0.011437%,6,0,6,0,0,0
_printf_g.o,0.011437%,6,0,6,0,0,0
_printf_f.o,0.011437%,6,0,6,0,0,0
_printf_e.o,0.011437%,6,0,6,0,0,0
_printf_d.o,0.011437%,6,0,6,0,0,0
_printf_c.o,0.011437%,6,0,6,0,0,0
_printf_a.o,0.011437%,6,0,6,0,0,0
__rtentry4.o,0.011437%,6,0,6,0,0,0
printf2.o,0.007625%,4,0,4,0,0,0
printf1.o,0.007625%,4,0,4,0,0,0
_printf_percent_end.o,0.007625%,4,0,4,0,0,0
use_no_semi.o,0.003812%,2,0,2,0,0,0
rtexit.o,0.003812%,2,0,2,0,0,0
libshutdown2.o,0.003812%,2,0,2,0,0,0
libshutdown.o,0.003812%,2,0,2,0,0,0
libinit.o,0.003812%,2,0,2,0,0,0
